<?php

declare(strict_types=1);

use App\Shared\Application\Command\CommandBusInterface;
use App\Shared\Application\Query\QueryBusInterface;
use App\Shared\Infrastructure\Symfony\Messenger\MessengerCommandBus;
use App\Shared\Infrastructure\Symfony\Messenger\MessengerQueryBus;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use function Symfony\Component\DependencyInjection\Loader\Configurator\service;

return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure();

    $services->load('App\\Shared\\', dirname(__DIR__, 2).'/src/Shared')
        ->exclude([dirname(__DIR__, 2).'/src/Shared/Infrastructure/Symfony/Kernel.php']);

    // Configuration des bus de commandes et de requêtes
    $services->set(MessengerCommandBus::class)
        ->args([service('command.bus')]);

    $services->set(MessengerQueryBus::class)
        ->args([service('query.bus')]);

    // Alias pour les interfaces
    $services->alias(CommandBusInterface::class, MessengerCommandBus::class);
    $services->alias(QueryBusInterface::class, MessengerQueryBus::class);
};
