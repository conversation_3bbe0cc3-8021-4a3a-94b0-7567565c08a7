Flux de données

Controller reçoit la requête HTTP
Controller crée une Command/Query
Controller appelle le Use Case
Use Case utilise les services Domain et repositories
Repository (infrastructure) persiste les données
Controller retourne la réponse HTTP

Cette organisation sépare clairement les responsabilités et permet une grande flexibilité d'évolution !


Quand utiliser quoi ?
Utilisez DTO pour :

✅ Réponses d'API standardisées
✅ Validation des entrées
✅ Transformation de données
✅ Sérialisation/désérialisation

Utilisez Manager pour :
le manager s'occupe du crud 
✅ Opérations métier complexes
✅ Coordination de plusieurs services
✅ Transactions multi-étapes
✅ Logique d'orchestration

Alternative aux Managers : Les Use Cases qui ont le même rôle mais sont plus spécialisés (un Use Case = une action précise).


lors d'un test Unit je compare deux donnée entre elle celle de la fonction qui va etre crée createBook()sont resultat et de ceux que cela faire 


Request entrante (POST /books) via API Platform.

API Platform hydrate un CreateBookDto.

Le CreateBookProcessor (dans UI) reçoit ce DTO.

Il appelle un handler (dans Application) : CreateBookHandler.

Ce handler utilise un service du domaine (BookFactory, BookRepositoryInterface) pour appliquer les règles métier.

Le domaine crée un objet Book, lève des exceptions métiers si besoin.

Le repository Doctrine (dans Infrastructure) enregistre l’objet.

Une réponse est con$bookRepositorystruite avec un DTO + Transformer.

🧠 Pourquoi cette architecture est intéressante ?
Séparation des responsabilités stricte : domain, application, infrastructure, UI sont indépendants.

Facile à tester : le domaine peut être testé sans Symfony.

Scalable : on peut changer d’interface (CLI, API, etc) sans toucher au cœur métier.

Compatible avec API Platform tout en gardant le contrôle sur les couches

livebox 1G forfait 2990 par mois

pack special 

indepndanct 

box telephone appel 15.99 sans engagement 

CQRS + Bus (CommandBus / QueryBus)

Tu gardes toujours :

Entity

Repository

Controller

Mais tu ajoutes :

Command = un objet qui décrit ce qu’on veut faire (ex: CreateUserCommand)

CommandHandler = qui va exécuter la logique (ex: vérifier, valider, enregistrer)

CommandBus = envoie la commande au bon handler

Pareil pour les Query / QueryHandler / QueryBus

 Query + un QueryHandler.

 Le controller ne connaît pas la logique métier
 = $this->queryBus->dispatch(new GetActiveUsersQuery());

https://medium.com/beyn-technology/cqrs-principle-with-symfony-messenger-6c0fb2c28917