{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3.9.5", "doctrine/doctrine-bundle": ">=2.15", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.5", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2.7.1", "symfony/framework-bundle": "7.3.*", "symfony/messenger": "7.3.*", "symfony/runtime": "7.3.*", "symfony/uid": "7.3.*", "symfony/yaml": "7.3.*"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^4.1", "phpunit/phpunit": "^12.2.5", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/maker-bundle": ">=1.63"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}}