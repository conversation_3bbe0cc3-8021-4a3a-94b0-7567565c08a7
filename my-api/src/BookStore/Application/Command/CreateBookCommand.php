<?php

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\Model\Book;
use App\Shared\Application\Command\CommandInterface;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\BookAuthor;
use App\BookStore\Domain\ValueObject\BookPrice;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookContent;

/*
    implements CommandInterface<Book>
*/
final readonly class CreateBookCommand implements CommandInterface
{
    public function __construct(
        public BookName $name,
        public BookDescription $description,
        public BookAuthor $author,
        public BookContent $content,
        public BookPrice $price
    ){}
}